/**
 * 病人页面生命周期管理工具
 * 用于管理病人页面的初始化和清理，确保数据隔离
 */

import store from '@/store'
import { EventBus } from '@/utils/event-bus'

/**
 * 病人页面初始化
 * @param {string} patientId - 病人ID
 */
export function initPatientPage(patientId) {
  // 初始化病人标签页状态
  store.dispatch('patientTabs/initPatientTabs', patientId)
}

/**
 * 病人页面清理
 * @param {string} patientId - 病人ID
 */
export function cleanupPatientPage(patientId) {
  // 清理病人标签页状态
  store.dispatch('patientTabs/clearPatientTabs', patientId)

  // 清理相关的EventBus监听器
  const sidebarClickEvent = `sidebarClick:${patientId}`
  const activeRouteEvent = `activeRoute:${patientId}`

  EventBus.$off(sidebarClickEvent)
  EventBus.$off(activeRouteEvent)
}

/**
 * 获取病人特定的事件名称
 * @param {string} patientId - 病人ID
 * @param {string} eventType - 事件类型
 * @returns {string} 带命名空间的事件名称
 */
export function getPatientEventName(patientId, eventType) {
  return `${eventType}:${patientId}`
}

/**
 * 发送病人特定的事件
 * @param {string} patientId - 病人ID
 * @param {string} eventType - 事件类型
 * @param {any} payload - 事件数据
 */
export function emitPatientEvent(patientId, eventType, payload) {
  const eventName = getPatientEventName(patientId, eventType)
  EventBus.$emit(eventName, payload)
}

/**
 * 监听病人特定的事件
 * @param {string} patientId - 病人ID
 * @param {string} eventType - 事件类型
 * @param {Function} handler - 事件处理函数
 */
export function onPatientEvent(patientId, eventType, handler) {
  const eventName = getPatientEventName(patientId, eventType)
  EventBus.$on(eventName, handler)
}

/**
 * 取消监听病人特定的事件
 * @param {string} patientId - 病人ID
 * @param {string} eventType - 事件类型
 * @param {Function} handler - 事件处理函数（可选）
 */
export function offPatientEvent(patientId, eventType, handler) {
  const eventName = getPatientEventName(patientId, eventType)
  if (handler) {
    EventBus.$off(eventName, handler)
  } else {
    EventBus.$off(eventName)
  }
}

/**
 * Vue mixin，用于自动管理病人页面的生命周期
 */
export const PatientLifecycleMixin = {
  computed: {
    patientId() {
      return this.$route.params.id
    }
  },

  created() {
    if (this.patientId) {
      initPatientPage(this.patientId)
    }
  },

  beforeDestroy() {
    if (this.patientId) {
      cleanupPatientPage(this.patientId)
    }
  },

  methods: {
    // 便捷方法：发送病人事件
    $emitPatientEvent(eventType, payload) {
      emitPatientEvent(this.patientId, eventType, payload)
    },

    // 便捷方法：监听病人事件
    $onPatientEvent(eventType, handler) {
      onPatientEvent(this.patientId, eventType, handler)
    },

    // 便捷方法：取消监听病人事件
    $offPatientEvent(eventType, handler) {
      offPatientEvent(this.patientId, eventType, handler)
    }
  }
}
