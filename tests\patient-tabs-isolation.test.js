/**
 * 病人标签页数据隔离测试
 */

import { createLocalVue, mount } from '@vue/test-utils'
import Vuex from 'vuex'
import patientTabsModule from '@/store/modules/patientTabs'
import { EventBus } from '@/utils/event-bus'
import { 
  initPatientPage, 
  cleanupPatientPage, 
  getPatientEventName,
  emitPatientEvent,
  onPatientEvent,
  offPatientEvent
} from '@/utils/patient-lifecycle'

const localVue = createLocalVue()
localVue.use(Vuex)

describe('病人标签页数据隔离', () => {
  let store

  beforeEach(() => {
    store = new Vuex.Store({
      modules: {
        patientTabs: patientTabsModule
      }
    })
  })

  afterEach(() => {
    // 清理EventBus监听器
    EventBus.$off()
  })

  describe('Store模块测试', () => {
    test('应该能初始化病人标签页状态', () => {
      const patientId = 'patient1'
      store.dispatch('patientTabs/initPatientTabs', patientId)
      
      const state = store.getters['patientTabs/getPatientTabsState'](patientId)
      expect(state).toEqual({
        visitedViews: [],
        activeRoute: '',
        refreshKey: 0
      })
    })

    test('应该能为不同病人维护独立状态', () => {
      const patient1 = 'patient1'
      const patient2 = 'patient2'
      
      // 初始化两个病人
      store.dispatch('patientTabs/initPatientTabs', patient1)
      store.dispatch('patientTabs/initPatientTabs', patient2)
      
      // 为病人1添加标签页
      const tab1 = { name: 'tab1', meta: { title: '标签1' } }
      store.dispatch('patientTabs/addPatientTab', { patientId: patient1, tab: tab1 })
      
      // 为病人2添加不同的标签页
      const tab2 = { name: 'tab2', meta: { title: '标签2' } }
      store.dispatch('patientTabs/addPatientTab', { patientId: patient2, tab: tab2 })
      
      // 验证状态隔离
      const views1 = store.getters['patientTabs/getPatientVisitedViews'](patient1)
      const views2 = store.getters['patientTabs/getPatientVisitedViews'](patient2)
      
      expect(views1).toHaveLength(1)
      expect(views2).toHaveLength(1)
      expect(views1[0].name).toBe('tab1')
      expect(views2[0].name).toBe('tab2')
    })

    test('应该能正确移除标签页', () => {
      const patientId = 'patient1'
      store.dispatch('patientTabs/initPatientTabs', patientId)
      
      // 添加两个标签页
      const tab1 = { name: 'tab1', meta: { title: '标签1' } }
      const tab2 = { name: 'tab2', meta: { title: '标签2' } }
      store.dispatch('patientTabs/addPatientTab', { patientId, tab: tab1 })
      store.dispatch('patientTabs/addPatientTab', { patientId, tab: tab2 })
      
      // 移除第一个标签页
      store.dispatch('patientTabs/removePatientTab', { patientId, tabName: 'tab1' })
      
      const views = store.getters['patientTabs/getPatientVisitedViews'](patientId)
      expect(views).toHaveLength(1)
      expect(views[0].name).toBe('tab2')
    })

    test('应该能清理病人状态', () => {
      const patientId = 'patient1'
      store.dispatch('patientTabs/initPatientTabs', patientId)
      
      const tab = { name: 'tab1', meta: { title: '标签1' } }
      store.dispatch('patientTabs/addPatientTab', { patientId, tab })
      
      // 清理状态
      store.dispatch('patientTabs/clearPatientTabs', patientId)
      
      const views = store.getters['patientTabs/getPatientVisitedViews'](patientId)
      expect(views).toEqual([])
    })
  })

  describe('事件命名空间测试', () => {
    test('应该生成正确的事件名称', () => {
      const patientId = 'patient123'
      const eventType = 'sidebarClick'
      
      const eventName = getPatientEventName(patientId, eventType)
      expect(eventName).toBe('sidebarClick:patient123')
    })

    test('应该能发送和接收病人特定事件', (done) => {
      const patientId = 'patient1'
      const eventType = 'testEvent'
      const testData = { message: 'test' }
      
      // 监听事件
      onPatientEvent(patientId, eventType, (data) => {
        expect(data).toEqual(testData)
        done()
      })
      
      // 发送事件
      emitPatientEvent(patientId, eventType, testData)
    })

    test('不同病人的事件应该隔离', () => {
      const patient1 = 'patient1'
      const patient2 = 'patient2'
      const eventType = 'testEvent'
      
      let patient1Received = false
      let patient2Received = false
      
      // 为两个病人监听同类型事件
      onPatientEvent(patient1, eventType, () => {
        patient1Received = true
      })
      
      onPatientEvent(patient2, eventType, () => {
        patient2Received = true
      })
      
      // 只向病人1发送事件
      emitPatientEvent(patient1, eventType, {})
      
      // 验证只有病人1收到事件
      expect(patient1Received).toBe(true)
      expect(patient2Received).toBe(false)
    })

    test('应该能正确取消事件监听', () => {
      const patientId = 'patient1'
      const eventType = 'testEvent'
      
      let received = false
      const handler = () => {
        received = true
      }
      
      // 监听事件
      onPatientEvent(patientId, eventType, handler)
      
      // 取消监听
      offPatientEvent(patientId, eventType, handler)
      
      // 发送事件
      emitPatientEvent(patientId, eventType, {})
      
      // 验证没有收到事件
      expect(received).toBe(false)
    })
  })

  describe('生命周期管理测试', () => {
    test('应该能初始化病人页面', () => {
      const patientId = 'patient1'
      initPatientPage(patientId)
      
      const state = store.getters['patientTabs/getPatientTabsState'](patientId)
      expect(state.visitedViews).toEqual([])
    })

    test('应该能清理病人页面', () => {
      const patientId = 'patient1'
      
      // 初始化并添加一些状态
      initPatientPage(patientId)
      const tab = { name: 'tab1', meta: { title: '标签1' } }
      store.dispatch('patientTabs/addPatientTab', { patientId, tab })
      
      // 添加事件监听器
      let eventReceived = false
      onPatientEvent(patientId, 'testEvent', () => {
        eventReceived = true
      })
      
      // 清理
      cleanupPatientPage(patientId)
      
      // 验证状态被清理
      const views = store.getters['patientTabs/getPatientVisitedViews'](patientId)
      expect(views).toEqual([])
      
      // 验证事件监听器被清理
      emitPatientEvent(patientId, 'testEvent', {})
      expect(eventReceived).toBe(false)
    })
  })
})
