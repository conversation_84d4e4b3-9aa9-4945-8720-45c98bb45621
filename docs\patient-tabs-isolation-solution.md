# 病人标签页数据隔离解决方案

## 问题背景

当前系统存在两级标签页架构：
1. **第一级**：病人标签页（已通过keep-alive实现不同病人数据隔离）
2. **第二级**：病人内页标签（存在数据交叉污染问题）

### 主要问题
- 所有病人实例共享同一个EventBus，导致事件交叉污染
- `EventBus.$emit('sidebarClick')`和`EventBus.$emit('activeRoute')`事件在所有病人实例间共享
- 第二级标签页的状态没有按病人ID进行隔离

## 解决方案

采用**基于病人ID的事件命名空间**方案，保持现有架构优势的同时实现数据隔离。

### 核心思路
1. **事件命名空间化**：将EventBus事件名称加上病人ID前缀
2. **状态按病人隔离**：为每个病人维护独立的标签页状态
3. **生命周期管理**：自动清理病人页面关闭时的状态

## 实现细节

### 1. 新增Store模块 (`src/store/modules/patientTabs.js`)

```javascript
// 为每个病人ID维护独立的标签页状态
const state = {
  patientTabsState: {} // { patientId: { visitedViews: [], activeRoute: '', refreshKey: 0 } }
}
```

**主要功能**：
- `initPatientTabs`: 初始化病人标签页状态
- `addPatientTab`: 添加病人标签页
- `removePatientTab`: 移除病人标签页
- `setPatientActiveRoute`: 设置活跃标签页
- `clearPatientTabs`: 清理病人标签页状态

### 2. 事件命名空间化

**修改前**：
```javascript
EventBus.$emit('sidebarClick', menu)
EventBus.$on('sidebarClick', handler)
```

**修改后**：
```javascript
EventBus.$emit(`sidebarClick:${patientId}`, menu)
EventBus.$on(`sidebarClick:${patientId}`, handler)
```

### 3. 生命周期管理工具 (`src/utils/patient-lifecycle.js`)

提供统一的病人页面生命周期管理：
- `initPatientPage(patientId)`: 初始化病人页面
- `cleanupPatientPage(patientId)`: 清理病人页面状态
- `PatientLifecycleMixin`: Vue mixin，自动管理生命周期

### 4. 组件更新

#### TagsView组件 (`src/views/patient-detail/components/TagsView/index.vue`)
- 移除本地状态管理（visitedViews, activeRoute, refreshKey）
- 使用store的getter获取病人特定的状态
- 使用命名空间化的事件名称

#### Sidebar组件 (`src/views/patient-detail/components/Sidebar/index.vue`)
- 添加病人ID相关的computed属性
- 使用命名空间化的事件发送

#### PatientDetailView组件
- 添加sidebarClickEvent computed属性
- 更新事件发送使用命名空间

## 优势

### 1. 数据隔离
- 每个病人的标签页状态完全独立
- 事件不会跨病人实例传播
- 避免了数据交叉污染

### 2. 保持现有优势
- 继续使用Vue的keep-alive机制
- 保持TagsView系统的功能
- 最小化代码改动

### 3. 自动清理
- 病人页面关闭时自动清理相关状态
- 防止内存泄漏
- 确保系统稳定性

### 4. 易于维护
- 清晰的命名空间规则
- 统一的生命周期管理
- 便于调试和排查问题

## 使用方式

### 1. 在病人页面组件中使用mixin
```javascript
import { PatientLifecycleMixin } from '@/utils/patient-lifecycle'

export default {
  mixins: [PatientLifecycleMixin],
  // 自动获得patientId computed属性
  // 自动管理生命周期
}
```

### 2. 发送病人特定事件
```javascript
// 使用mixin提供的便捷方法
this.$emitPatientEvent('sidebarClick', menuData)

// 或直接使用工具函数
import { emitPatientEvent } from '@/utils/patient-lifecycle'
emitPatientEvent(patientId, 'sidebarClick', menuData)
```

### 3. 监听病人特定事件
```javascript
// 在mounted中监听
this.$onPatientEvent('activeRoute', this.handleActiveRoute)

// 在destroyed中清理
this.$offPatientEvent('activeRoute', this.handleActiveRoute)
```

## 测试验证

### 验证步骤
1. 打开多个病人标签页
2. 在不同病人页面中切换内部标签
3. 验证标签页状态不会相互影响
4. 关闭病人标签页，验证状态被正确清理

### 预期结果
- 每个病人的内部标签页状态独立
- 切换病人标签页时保持各自的子标签状态
- 无重复API请求
- 无内存泄漏

## 注意事项

1. **事件命名规范**：所有病人相关事件必须使用命名空间
2. **生命周期管理**：确保在适当的时机清理状态
3. **向后兼容**：现有功能保持不变
4. **性能考虑**：状态按需创建和清理

## 扩展性

该方案具有良好的扩展性：
- 可以轻松添加新的病人特定事件
- 支持更复杂的状态管理需求
- 可以应用到其他需要数据隔离的场景
