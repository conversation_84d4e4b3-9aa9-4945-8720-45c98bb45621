# 病人标签页数据隔离解决方案

## 问题背景

当前系统存在两级标签页架构：
1. **第一级**：病人标签页（已通过keep-alive实现不同病人数据隔离）
2. **第二级**：病人内页标签（存在数据交叉污染问题）

### 主要问题
- 所有病人实例共享同一个EventBus，导致事件交叉污染
- `EventBus.$emit('sidebarClick')`和`EventBus.$emit('activeRoute')`事件在所有病人实例间共享
- 第二级标签页的状态没有按病人ID进行隔离

## 解决方案

采用**基于病人ID的事件命名空间**方案，保持现有架构优势的同时实现数据隔离。

### 核心思路
1. **事件命名空间化**：将EventBus事件名称加上病人ID前缀
2. **状态按病人隔离**：为每个病人维护独立的标签页状态
3. **生命周期管理**：自动清理病人页面关闭时的状态

## 实现细节

### 1. 新增Store模块 (`src/store/modules/patientTabs.js`)

```javascript
// 为每个病人ID维护独立的标签页状态
const state = {
  patientTabsState: {} // { patientId: { visitedViews: [], activeRoute: '', refreshKey: 0 } }
}
```

**主要功能**：
- `initPatientTabs`: 初始化病人标签页状态
- `addPatientTab`: 添加病人标签页
- `removePatientTab`: 移除病人标签页
- `setPatientActiveRoute`: 设置活跃标签页
- `clearPatientTabs`: 清理病人标签页状态

### 2. 事件命名空间化

**修改前**：
```javascript
EventBus.$emit('sidebarClick', menu)
EventBus.$on('sidebarClick', handler)
```

**修改后**：
```javascript
EventBus.$emit(`sidebarClick:${patientId}`, menu)
EventBus.$on(`sidebarClick:${patientId}`, handler)
```

### 3. 生命周期管理工具 (`src/utils/patient-lifecycle.js`)

提供统一的病人页面生命周期管理：
- `initPatientPage(patientId)`: 初始化病人页面
- `cleanupPatientPage(patientId)`: 清理病人页面状态
- `PatientLifecycleMixin`: Vue mixin，自动管理生命周期

### 4. 组件更新

#### TagsView组件 (`src/views/patient-detail/components/TagsView/index.vue`)
- 移除本地状态管理（visitedViews, activeRoute, refreshKey）
- 使用store的getter获取病人特定的状态
- 使用命名空间化的事件名称

#### Sidebar组件 (`src/views/patient-detail/components/Sidebar/index.vue`)
- 添加病人ID相关的computed属性
- 使用命名空间化的事件发送

#### PatientDetailView组件
- 添加sidebarClickEvent computed属性
- 更新事件发送使用命名空间

## 优势

### 1. 数据隔离
- 每个病人的标签页状态完全独立
- 事件不会跨病人实例传播
- 避免了数据交叉污染

### 2. 保持现有优势
- 继续使用Vue的keep-alive机制
- 保持TagsView系统的功能
- 最小化代码改动

### 3. 自动清理
- 病人页面关闭时自动清理相关状态
- 防止内存泄漏
- 确保系统稳定性

### 4. 易于维护
- 清晰的命名空间规则
- 统一的生命周期管理
- 便于调试和排查问题

## 使用方式

### 1. 在病人页面组件中使用mixin
```javascript
import { PatientLifecycleMixin } from '@/utils/patient-lifecycle'

export default {
  mixins: [PatientLifecycleMixin],
  // 自动获得patientId computed属性
  // 自动管理生命周期
}
```

### 2. 发送病人特定事件
```javascript
// 使用mixin提供的便捷方法
this.$emitPatientEvent('sidebarClick', menuData)

// 或直接使用工具函数
import { emitPatientEvent } from '@/utils/patient-lifecycle'
emitPatientEvent(patientId, 'sidebarClick', menuData)
```

### 3. 监听病人特定事件
```javascript
// 在mounted中监听
this.$onPatientEvent('activeRoute', this.handleActiveRoute)

// 在destroyed中清理
this.$offPatientEvent('activeRoute', this.handleActiveRoute)
```

## 测试验证

### 验证步骤
1. 打开多个病人标签页
2. 在不同病人页面中切换内部标签
3. 验证标签页状态不会相互影响
4. 关闭病人标签页，验证状态被正确清理

### 预期结果
- 每个病人的内部标签页状态独立
- 切换病人标签页时保持各自的子标签状态
- 无重复API请求
- 无内存泄漏

## 问题修复

### 修复的具体问题

#### 1. 默认标签页初始化问题
**问题**：打开病人详情页面时，默认的"住院医嘱"标签页不显示或不激活。

**解决方案**：
- 在`initTags()`方法中添加了`$nextTick`确保DOM更新后设置activeRoute
- 使用async/await确保状态初始化的正确顺序
- 添加了状态检查，确保默认标签页被正确激活

#### 2. 页面实时更新问题
**问题**：点击侧边栏菜单时，二级标签页没有实时切换和显示对应内容。

**解决方案**：
- 修复了组件key值，包含病人ID确保不同病人使用不同组件实例
- 添加了病人ID变化的监听器，自动重新初始化标签页
- 优化了事件监听器的管理，避免事件丢失

#### 3. 组件内部数据隔离问题
**问题**：标签页内的组件仍然共享数据，存在跨病人数据污染。

**解决方案**：
- 创建了`PatientIsolatedComponent`包装器组件
- 提供了`PatientDataIsolationMixin`帮助组件处理数据隔离
- 实现了病人特定的存储、API参数和iframe URL生成
- 确保所有组件实例按病人ID完全隔离

### 新增组件和工具

#### 1. PatientIsolatedComponent (`src/components/PatientIsolatedComponent.vue`)
- 组件包装器，确保子组件按病人ID隔离
- 提供病人特定的provide/inject机制
- 生成唯一的组件key值

#### 2. PatientDataIsolationMixin (`src/mixins/patient-data-isolation.js`)
- 提供病人特定的本地存储和会话存储方法
- 生成病人特定的API参数和iframe URL
- 提供病人事件发送机制

#### 3. 示例组件 (`src/components/examples/PatientIsolatedExample.vue`)
- 演示如何正确使用数据隔离功能
- 提供最佳实践参考

### 关键改进

#### 1. 组件Key值策略
```javascript
// 旧的key值（可能导致组件实例共享）
:key="`${tag.name}-${refreshKey}`"

// 新的key值（确保病人间完全隔离）
:key="`${patientId}-${tag.name}-${refreshKey}`"
```

#### 2. 状态管理优化
- 修复了removePatientTab中的状态同步问题
- 确保activeRoute在标签页关闭后正确更新
- 添加了病人ID变化的响应机制

#### 3. 数据隔离机制
```javascript
// 病人特定的存储
this.setPatientLocalStorage('key', value)
this.getPatientLocalStorage('key', defaultValue)

// 病人特定的API参数
const params = this.getPatientApiParams({ action: 'test' })

// 病人特定的iframe URL
const url = this.getPatientIframeUrl('/page.html', { extra: 'param' })
```

## 使用指南

### 1. 在现有组件中添加数据隔离
```javascript
import PatientDataIsolationMixin from '@/mixins/patient-data-isolation'

export default {
  mixins: [PatientDataIsolationMixin],
  // 现在可以使用病人特定的方法
}
```

### 2. 创建新的病人相关组件
```vue
<template>
  <patient-isolated-component
    :component="YourComponent"
    :patient-id="patientId"
    :extra-key="uniqueKey"
  />
</template>
```

### 3. 处理iframe组件
```javascript
// 生成包含病人ID的iframe URL
const iframeUrl = this.getPatientIframeUrl('/medical-record.html', {
  recordType: 'admission',
  mode: 'edit'
})
```

## 注意事项

1. **事件命名规范**：所有病人相关事件必须使用命名空间
2. **生命周期管理**：确保在适当的时机清理状态
3. **向后兼容**：现有功能保持不变
4. **性能考虑**：状态按需创建和清理
5. **组件隔离**：使用PatientIsolatedComponent包装需要隔离的组件
6. **数据存储**：使用mixin提供的方法进行病人特定的数据存储

## 扩展性

该方案具有良好的扩展性：
- 可以轻松添加新的病人特定事件
- 支持更复杂的状态管理需求
- 可以应用到其他需要数据隔离的场景
- 提供了完整的数据隔离工具链
