/**
 * 病人数据隔离Mixin
 * 为组件提供病人数据隔离功能，确保不同病人的数据不会相互污染
 */

export default {
  inject: {
    // 注入病人ID和隔离键
    patientId: {
      from: 'patientId',
      default() {
        return this.$route?.params?.id || ''
      }
    },
    isolationKey: {
      from: 'isolationKey',
      default() {
        return `patient-${this.patientId}`
      }
    },
    $emitPatientEvent: {
      from: '$emitPatientEvent',
      default() {
        return () => {}
      }
    }
  },

  computed: {
    // 生成病人特定的缓存键
    patientCacheKey() {
      return `${this.isolationKey}-cache`
    },
    
    // 生成病人特定的存储键
    patientStorageKey() {
      return `${this.isolationKey}-storage`
    }
  },

  methods: {
    /**
     * 获取病人特定的本地存储数据
     * @param {string} key - 存储键
     * @param {any} defaultValue - 默认值
     * @returns {any} 存储的数据
     */
    getPatientLocalStorage(key, defaultValue = null) {
      try {
        const storageKey = `${this.patientStorageKey}-${key}`
        const data = localStorage.getItem(storageKey)
        return data ? JSON.parse(data) : defaultValue
      } catch (error) {
        console.warn('Failed to get patient local storage:', error)
        return defaultValue
      }
    },

    /**
     * 设置病人特定的本地存储数据
     * @param {string} key - 存储键
     * @param {any} value - 要存储的数据
     */
    setPatientLocalStorage(key, value) {
      try {
        const storageKey = `${this.patientStorageKey}-${key}`
        localStorage.setItem(storageKey, JSON.stringify(value))
      } catch (error) {
        console.warn('Failed to set patient local storage:', error)
      }
    },

    /**
     * 移除病人特定的本地存储数据
     * @param {string} key - 存储键
     */
    removePatientLocalStorage(key) {
      try {
        const storageKey = `${this.patientStorageKey}-${key}`
        localStorage.removeItem(storageKey)
      } catch (error) {
        console.warn('Failed to remove patient local storage:', error)
      }
    },

    /**
     * 获取病人特定的会话存储数据
     * @param {string} key - 存储键
     * @param {any} defaultValue - 默认值
     * @returns {any} 存储的数据
     */
    getPatientSessionStorage(key, defaultValue = null) {
      try {
        const storageKey = `${this.patientStorageKey}-${key}`
        const data = sessionStorage.getItem(storageKey)
        return data ? JSON.parse(data) : defaultValue
      } catch (error) {
        console.warn('Failed to get patient session storage:', error)
        return defaultValue
      }
    },

    /**
     * 设置病人特定的会话存储数据
     * @param {string} key - 存储键
     * @param {any} value - 要存储的数据
     */
    setPatientSessionStorage(key, value) {
      try {
        const storageKey = `${this.patientStorageKey}-${key}`
        sessionStorage.setItem(storageKey, JSON.stringify(value))
      } catch (error) {
        console.warn('Failed to set patient session storage:', error)
      }
    },

    /**
     * 移除病人特定的会话存储数据
     * @param {string} key - 存储键
     */
    removePatientSessionStorage(key) {
      try {
        const storageKey = `${this.patientStorageKey}-${key}`
        sessionStorage.removeItem(storageKey)
      } catch (error) {
        console.warn('Failed to remove patient session storage:', error)
      }
    },

    /**
     * 生成病人特定的API请求参数
     * @param {object} params - 基础参数
     * @returns {object} 包含病人ID的参数
     */
    getPatientApiParams(params = {}) {
      return {
        ...params,
        bingLiID: this.patientId,
        patientId: this.patientId
      }
    },

    /**
     * 生成病人特定的iframe URL
     * @param {string} baseUrl - 基础URL
     * @param {object} extraParams - 额外参数
     * @returns {string} 完整的URL
     */
    getPatientIframeUrl(baseUrl, extraParams = {}) {
      const params = this.getPatientApiParams(extraParams)
      const urlParams = new URLSearchParams()
      
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          urlParams.append(key, params[key])
        }
      })
      
      const separator = baseUrl.includes('?') ? '&' : '?'
      return `${baseUrl}${separator}${urlParams.toString()}`
    },

    /**
     * 清理病人特定的所有存储数据
     */
    clearPatientStorage() {
      const prefix = this.patientStorageKey
      
      // 清理localStorage
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(prefix)) {
          localStorage.removeItem(key)
        }
      })
      
      // 清理sessionStorage
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith(prefix)) {
          sessionStorage.removeItem(key)
        }
      })
    },

    /**
     * 发送病人特定事件
     * @param {string} eventType - 事件类型
     * @param {any} payload - 事件数据
     */
    emitPatientEvent(eventType, payload) {
      if (this.$emitPatientEvent) {
        this.$emitPatientEvent(eventType, payload)
      }
    }
  },

  beforeDestroy() {
    // 组件销毁时可以选择清理存储数据
    // this.clearPatientStorage()
  }
}
