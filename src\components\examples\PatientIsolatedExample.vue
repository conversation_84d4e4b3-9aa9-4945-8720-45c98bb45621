<!--
  病人数据隔离示例组件
  演示如何在组件中正确使用病人数据隔离功能
-->
<template>
  <div class="patient-isolated-example">
    <h3>病人数据隔离示例</h3>
    <p>当前病人ID: {{ patientId }}</p>
    <p>隔离键: {{ isolationKey }}</p>
    
    <div class="example-section">
      <h4>本地存储测试</h4>
      <el-input 
        v-model="localStorageValue" 
        placeholder="输入要存储的值"
        @input="saveToLocalStorage"
      />
      <p>存储的值: {{ savedLocalStorageValue }}</p>
    </div>
    
    <div class="example-section">
      <h4>会话存储测试</h4>
      <el-input 
        v-model="sessionStorageValue" 
        placeholder="输入要存储的值"
        @input="saveToSessionStorage"
      />
      <p>存储的值: {{ savedSessionStorageValue }}</p>
    </div>
    
    <div class="example-section">
      <h4>iframe URL生成测试</h4>
      <p>基础URL: {{ baseUrl }}</p>
      <p>生成的URL: {{ generatedUrl }}</p>
    </div>
    
    <div class="example-section">
      <h4>API参数生成测试</h4>
      <pre>{{ JSON.stringify(apiParams, null, 2) }}</pre>
    </div>
    
    <div class="example-section">
      <h4>事件测试</h4>
      <el-button @click="sendTestEvent">发送测试事件</el-button>
    </div>
  </div>
</template>

<script>
import PatientDataIsolationMixin from '@/mixins/patient-data-isolation'

export default {
  name: 'PatientIsolatedExample',
  mixins: [PatientDataIsolationMixin],
  
  data() {
    return {
      localStorageValue: '',
      sessionStorageValue: '',
      baseUrl: '/example/page.html'
    }
  },
  
  computed: {
    savedLocalStorageValue() {
      return this.getPatientLocalStorage('testValue', '无数据')
    },
    
    savedSessionStorageValue() {
      return this.getPatientSessionStorage('testValue', '无数据')
    },
    
    generatedUrl() {
      return this.getPatientIframeUrl(this.baseUrl, {
        extra: 'test',
        timestamp: Date.now()
      })
    },
    
    apiParams() {
      return this.getPatientApiParams({
        action: 'test',
        module: 'example'
      })
    }
  },
  
  mounted() {
    // 加载保存的数据
    this.localStorageValue = this.getPatientLocalStorage('testValue', '')
    this.sessionStorageValue = this.getPatientSessionStorage('testValue', '')
  },
  
  methods: {
    saveToLocalStorage() {
      this.setPatientLocalStorage('testValue', this.localStorageValue)
    },
    
    saveToSessionStorage() {
      this.setPatientSessionStorage('testValue', this.sessionStorageValue)
    },
    
    sendTestEvent() {
      this.emitPatientEvent('testEvent', {
        message: '这是一个测试事件',
        timestamp: new Date().toISOString(),
        patientId: this.patientId
      })
    }
  }
}
</script>

<style scoped>
.patient-isolated-example {
  padding: 20px;
  max-width: 800px;
}

.example-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.example-section h4 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
