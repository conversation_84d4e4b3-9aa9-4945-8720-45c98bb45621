<!--
  病人数据隔离组件包装器
  确保组件内部数据按病人ID隔离，避免跨病人数据污染
-->
<template>
  <div class="patient-isolated-component" :key="componentKey">
    <component 
      :is="component" 
      v-bind="$attrs" 
      v-on="$listeners"
      :patient-id="patientId"
      :isolation-key="isolationKey"
    />
  </div>
</template>

<script>
export default {
  name: 'PatientIsolatedComponent',
  inheritAttrs: false,
  props: {
    // 要渲染的组件
    component: {
      type: [String, Object, Function],
      required: true
    },
    // 病人ID
    patientId: {
      type: String,
      required: true
    },
    // 额外的隔离键（可选）
    extraKey: {
      type: String,
      default: ''
    }
  },
  computed: {
    // 生成唯一的隔离键
    isolationKey() {
      return `patient-${this.patientId}${this.extraKey ? `-${this.extraKey}` : ''}`
    },
    
    // 组件的唯一键，确保不同病人使用不同的组件实例
    componentKey() {
      return `${this.isolationKey}-${this.component.name || 'component'}`
    }
  },
  
  provide() {
    return {
      // 向子组件提供病人ID和隔离键
      patientId: this.patientId,
      isolationKey: this.isolationKey,
      
      // 提供病人特定的事件发送方法
      $emitPatientEvent: (eventType, payload) => {
        this.$emit('patient-event', {
          patientId: this.patientId,
          eventType,
          payload
        })
      }
    }
  },
  
  methods: {
    // 刷新组件实例
    refresh() {
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.patient-isolated-component {
  height: 100%;
  width: 100%;
}
</style>
