/**
 * 病人标签页状态管理
 * 为每个病人ID维护独立的标签页状态，避免数据交叉污染
 */

const state = {
  // 存储每个病人的标签页状态
  // 格式: { patientId: { visitedViews: [], activeRoute: '', refreshKey: 0 } }
  patientTabsState: {}
}

const mutations = {
  // 初始化病人标签页状态
  INIT_PATIENT_TABS: (state, patientId) => {
    if (!state.patientTabsState[patientId]) {
      state.patientTabsState[patientId] = {
        visitedViews: [],
        activeRoute: '',
        refreshKey: 0
      }
    }
  },

  // 设置病人的访问标签页列表
  SET_PATIENT_VISITED_VIEWS: (state, { patientId, visitedViews }) => {
    if (state.patientTabsState[patientId]) {
      state.patientTabsState[patientId].visitedViews = visitedViews
    }
  },

  // 添加病人标签页
  ADD_PATIENT_TAB: (state, { patientId, tab }) => {
    if (!state.patientTabsState[patientId]) {
      mutations.INIT_PATIENT_TABS(state, patientId)
    }

    const patientState = state.patientTabsState[patientId]
    if (!patientState.visitedViews.some((v) => v.name === tab.name)) {
      patientState.visitedViews.push({
        ...tab,
        title: tab.meta?.title || 'no-name'
      })
    }
  },

  // 设置病人当前活跃标签页
  SET_PATIENT_ACTIVE_ROUTE: (state, { patientId, activeRoute }) => {
    if (state.patientTabsState[patientId]) {
      state.patientTabsState[patientId].activeRoute = activeRoute
    }
  },

  // 移除病人标签页
  REMOVE_PATIENT_TAB: (state, { patientId, tabName }) => {
    if (state.patientTabsState[patientId]) {
      const index = state.patientTabsState[patientId].visitedViews.findIndex(
        (v) => v.name === tabName
      )
      if (index > -1) {
        state.patientTabsState[patientId].visitedViews.splice(index, 1)
      }
    }
  },

  // 刷新病人标签页
  REFRESH_PATIENT_TAB: (state, patientId) => {
    if (state.patientTabsState[patientId]) {
      state.patientTabsState[patientId].refreshKey += 1
    }
  },

  // 关闭其他病人标签页
  CLOSE_OTHER_PATIENT_TABS: (state, { patientId, currentTabName }) => {
    if (state.patientTabsState[patientId]) {
      state.patientTabsState[patientId].visitedViews = state.patientTabsState[
        patientId
      ].visitedViews.filter((v) => v.meta?.affix || v.name === currentTabName)
    }
  },

  // 关闭所有病人标签页
  CLOSE_ALL_PATIENT_TABS: (state, patientId) => {
    if (state.patientTabsState[patientId]) {
      state.patientTabsState[patientId].visitedViews = state.patientTabsState[
        patientId
      ].visitedViews.filter((v) => v.meta?.affix)
    }
  },

  // 清理病人标签页状态（当病人标签页关闭时）
  CLEAR_PATIENT_TABS: (state, patientId) => {
    delete state.patientTabsState[patientId]
  }
}

const actions = {
  // 初始化病人标签页
  initPatientTabs({ commit }, patientId) {
    commit('INIT_PATIENT_TABS', patientId)
  },

  // 添加病人标签页
  addPatientTab({ commit, state }, { patientId, tab }) {
    commit('ADD_PATIENT_TAB', { patientId, tab })
    commit('SET_PATIENT_ACTIVE_ROUTE', { patientId, activeRoute: tab.name })
  },

  // 设置活跃标签页
  setPatientActiveRoute({ commit }, { patientId, activeRoute }) {
    commit('SET_PATIENT_ACTIVE_ROUTE', { patientId, activeRoute })
  },

  // 移除标签页
  removePatientTab({ commit, state }, { patientId, tabName }) {
    const patientState = state.patientTabsState[patientId]
    if (!patientState) return

    commit('REMOVE_PATIENT_TAB', { patientId, tabName })

    // 如果关闭的是当前活跃标签页，需要切换到最后一个标签页
    if (patientState.activeRoute === tabName) {
      const visitedViews = patientState.visitedViews
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        commit('SET_PATIENT_ACTIVE_ROUTE', { patientId, activeRoute: latestView.name })
      } else {
        commit('SET_PATIENT_ACTIVE_ROUTE', { patientId, activeRoute: 'InPatientOrders' })
      }
    }
  },

  // 刷新标签页
  refreshPatientTab({ commit }, patientId) {
    commit('REFRESH_PATIENT_TAB', patientId)
  },

  // 关闭其他标签页
  closeOtherPatientTabs({ commit }, { patientId, currentTabName }) {
    commit('CLOSE_OTHER_PATIENT_TABS', { patientId, currentTabName })
  },

  // 关闭所有标签页
  closeAllPatientTabs({ commit }, patientId) {
    commit('CLOSE_ALL_PATIENT_TABS', patientId)
  },

  // 清理病人标签页状态
  clearPatientTabs({ commit }, patientId) {
    commit('CLEAR_PATIENT_TABS', patientId)
  }
}

const getters = {
  // 获取病人的标签页状态
  getPatientTabsState: (state) => (patientId) => {
    return (
      state.patientTabsState[patientId] || {
        visitedViews: [],
        activeRoute: '',
        refreshKey: 0
      }
    )
  },

  // 获取病人的访问标签页列表
  getPatientVisitedViews: (state) => (patientId) => {
    return state.patientTabsState[patientId]?.visitedViews || []
  },

  // 获取病人的当前活跃标签页
  getPatientActiveRoute: (state) => (patientId) => {
    return state.patientTabsState[patientId]?.activeRoute || ''
  },

  // 获取病人的刷新键
  getPatientRefreshKey: (state) => (patientId) => {
    return state.patientTabsState[patientId]?.refreshKey || 0
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
